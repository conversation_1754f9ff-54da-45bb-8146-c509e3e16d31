# EoDB Chatbot Dynamic Configuration Guide

## Overview

The EoDB Chatbot has been made fully dynamic, allowing all configurations to be managed from the database without touching the code. This guide explains how to use and manage the dynamic configuration system.

## Database Tables

### 1. `eodb_system_config_final`
Stores general system configuration values.

**Key Fields:**
- `config_key`: Unique identifier for the configuration
- `config_value`: The actual value
- `config_type`: Type of value (text, json, number, boolean)
- `description`: Human-readable description

**Example Configurations:**
- `chatbot_name`: Name of the chatbot
- `api_timeout`: API timeout in seconds
- `max_session_duration`: Maximum session duration
- `default_language`: Default language for responses

### 2. `eodb_greeting_messages_final`
Stores all greeting and prompt messages.

**Key Fields:**
- `message_key`: Unique identifier for the message
- `message_text`: The actual message text
- `message_type`: Type of message (initial, welcome, error, success, prompt)
- `language`: Language code (en, bn, etc.)

**Example Messages:**
- `initial_greeting`: Main welcome message
- `service_help_text`: Service help description
- `caf_prompt`: CAF number input prompt
- `otp_prompt`: OTP input prompt
- `coming_soon_message`: Message for disabled features

### 3. `eodb_keyword_redirects_final`
Manages keyword-based redirects.

**Key Fields:**
- `keyword`: The keyword to match
- `redirect_to_step`: Step number to redirect to
- `redirect_to_option`: Option to select after redirect
- `redirect_message`: Message to show during redirect
- `priority`: Priority for keyword matching (higher = checked first)

**Example Keywords:**
- `application status` → Redirects to CAF input
- `licence` → Redirects to service selection
- `clearance` → Redirects to service selection

### 4. `eodb_special_commands_final`
Manages special commands like exit, restart.

**Key Fields:**
- `command`: The command text
- `command_type`: Type of command (exit, restart, redirect, action)
- `response_message`: Message to show when command is executed
- `action_data`: JSON data for command actions

**Example Commands:**
- `exit`: Ends the chatbot session
- `restart`: Restarts the chatbot session
- `help`: Shows help information

### 5. `eodb_option_status_final`
Controls which main menu options are enabled/disabled.

**Key Fields:**
- `option_key`: Unique identifier for the option
- `option_name`: Display name of the option
- `is_enabled`: Whether the option is enabled
- `disabled_message`: Message to show when option is disabled
- `order_num`: Display order

## How to Make Changes

### 1. Changing Greeting Messages

```sql
-- Update the initial greeting
UPDATE eodb_greeting_messages_final 
SET message_text = 'Welcome to the new AI Assistant!' 
WHERE message_key = 'initial_greeting';

-- Update the service help text
UPDATE eodb_greeting_messages_final 
SET message_text = 'I can assist you with these services:' 
WHERE message_key = 'service_help_text';
```

### 2. Enabling/Disabling Options

```sql
-- Disable the FAQ option
UPDATE eodb_option_status_final 
SET is_enabled = false, 
    disabled_message = 'FAQ is temporarily unavailable.' 
WHERE option_key = 'faq';

-- Enable the Query/Grievance option
UPDATE eodb_option_status_final 
SET is_enabled = true, 
    disabled_message = '' 
WHERE option_key = 'raise_query';
```

### 3. Adding New Keywords

```sql
-- Add a new keyword redirect
INSERT INTO eodb_keyword_redirects_final 
(keyword, redirect_to_step, redirect_to_option, redirect_message, priority) 
VALUES 
('help me', 1, '', 'How can I help you today?', 5);
```

### 4. Modifying Special Commands

```sql
-- Update the exit message
UPDATE eodb_special_commands_final 
SET response_message = 'Goodbye! Thank you for using our service.' 
WHERE command = 'exit';

-- Add a new special command
INSERT INTO eodb_special_commands_final 
(command, command_type, response_message, action_data) 
VALUES 
('menu', 'redirect', 'Returning to main menu...', 
 '{"action": "goto_main_menu", "step": 1}');
```

### 5. System Configuration

```sql
-- Change the chatbot name
UPDATE eodb_system_config_final 
SET config_value = 'New Assistant Name' 
WHERE config_key = 'chatbot_name';

-- Update API timeout
UPDATE eodb_system_config_final 
SET config_value = '60' 
WHERE config_key = 'api_timeout';
```

## Admin Interface

Use the `admin_config.php` file to manage configurations through a web interface:

1. **Greeting Messages Tab**: Manage all greeting and prompt messages
2. **Option Status Tab**: Enable/disable main menu options
3. **Keyword Redirects Tab**: Manage keyword-based redirects
4. **Special Commands Tab**: Manage special commands
5. **System Config Tab**: Manage system-wide settings

## API Endpoints

### Get Configuration
```
GET /chatbot/config
```
Returns the current dynamic configuration for the frontend.

## Testing Changes

1. **Update Database**: Make your changes using SQL or the admin interface
2. **Restart FastAPI**: Restart the main.py server to ensure fresh database connections
3. **Test Frontend**: Use `test_frontend.php` to verify changes
4. **Check Logs**: Monitor the application logs for any errors

## Best Practices

1. **Backup Before Changes**: Always backup the database before making changes
2. **Test in Development**: Test all changes in a development environment first
3. **Use Transactions**: Wrap multiple related changes in database transactions
4. **Monitor Performance**: Keep an eye on database performance with many dynamic queries
5. **Cache When Possible**: Consider implementing caching for frequently accessed configurations

## Troubleshooting

### Configuration Not Loading
- Check database connection
- Verify table names are correct
- Check for SQL syntax errors in queries

### Frontend Not Updating
- Clear browser cache
- Check API endpoint is accessible
- Verify JSON response format

### Keywords Not Working
- Check keyword priority settings
- Verify is_active flag is true
- Test with exact keyword text

## Future Enhancements

The dynamic configuration system can be extended to include:
- Multi-language support
- A/B testing configurations
- Time-based configuration changes
- User role-based configurations
- Configuration versioning and rollback

## Support

For issues with the dynamic configuration system:
1. Check the application logs
2. Verify database connectivity
3. Test with the admin interface
4. Review this documentation

Remember: All changes are now database-driven, so no code deployment is needed for configuration updates!
