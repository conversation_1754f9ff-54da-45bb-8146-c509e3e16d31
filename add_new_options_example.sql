-- =====================================================
-- ADD NEW OPTIONS EXAMPLES FOR DYNAMIC EoDB CHATBOT
-- =====================================================
-- This file contains examples of how to add new options to the dynamic chatbot system
-- All additions are done via database INSERT statements - NO CODE CHANGES REQUIRED

-- =====================================================
-- 1. ADD NEW SECTORS
-- =====================================================

-- Add a new government sector (with serial number)
INSERT INTO master_data (type_id, value, order_num, is_active)
VALUES (
    (SELECT id FROM master_data_types WHERE type_name = 'sectors'),
    '32. Space Technology & Research',
    32,  -- Next order number
    TRUE
);

-- Add multiple new sectors at once (with serial numbers)
INSERT INTO master_data (type_id, value, order_num, is_active) VALUES
((SELECT id FROM master_data_types WHERE type_name = 'sectors'), '33. Artificial Intelligence & Machine Learning', 33, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'sectors'), '34. Blockchain & Cryptocurrency', 34, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'sectors'), '35. Renewable Energy & Solar', 35, TRUE);

-- =====================================================
-- 2. ADD NEW INVESTMENT RANGES
-- =====================================================

-- Add a new investment range (with serial number)
INSERT INTO master_data (type_id, value, order_num, is_active)
VALUES (
    (SELECT id FROM master_data_types WHERE type_name = 'investments'),
    '6. INR 1000 Cr. to INR 2000 Cr.',
    6,  -- Next order number
    TRUE
);

-- Add ultra-high investment category (with serial number)
INSERT INTO master_data (type_id, value, order_num, is_active)
VALUES (
    (SELECT id FROM master_data_types WHERE type_name = 'investments'),
    '7. More than INR 2000 Cr.',
    7,
    TRUE
);

-- =====================================================
-- 3. ADD NEW APPLICATION TYPES
-- =====================================================

-- Add a new application type (with serial number)
INSERT INTO master_data (type_id, value, order_num, is_active)
VALUES (
    (SELECT id FROM master_data_types WHERE type_name = 'application_types'),
    '5. Request for amendment/modification',
    5,
    TRUE
);

-- Add renewal application type (with serial number)
INSERT INTO master_data (type_id, value, order_num, is_active)
VALUES (
    (SELECT id FROM master_data_types WHERE type_name = 'application_types'),
    '6. Apply for renewal/extension',
    6,
    TRUE
);

-- =====================================================
-- 4. ADD NEW SERVICE TYPES
-- =====================================================

-- Add a new service category (with serial number)
INSERT INTO master_data (type_id, value, order_num, is_active)
VALUES (
    (SELECT id FROM master_data_types WHERE type_name = 'service_types'),
    '3. Post-operational',
    3,
    TRUE
);

-- Add compliance service type (with serial number)
INSERT INTO master_data (type_id, value, order_num, is_active)
VALUES (
    (SELECT id FROM master_data_types WHERE type_name = 'service_types'),
    '4. Compliance & Audit',
    4,
    TRUE
);

-- =====================================================
-- 5. ADD NEW PRE-ESTABLISHMENT SERVICES
-- =====================================================

-- Add new pre-establishment services (with serial numbers)
INSERT INTO master_data (type_id, value, order_num, is_active) VALUES
((SELECT id FROM master_data_types WHERE type_name = 'pre_establishment'), '17. Drone Operation License', 17, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'pre_establishment'), '18. AI/ML Data Processing License', 18, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'pre_establishment'), '19. Cryptocurrency Exchange License', 19, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'pre_establishment'), '20. Solar Panel Installation Permit', 20, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'pre_establishment'), '21. E-commerce Platform Registration', 21, TRUE);

-- =====================================================
-- 6. ADD NEW PRE-OPERATIONAL SERVICES
-- =====================================================

-- Add new pre-operational services (with serial numbers)
INSERT INTO master_data (type_id, value, order_num, is_active) VALUES
((SELECT id FROM master_data_types WHERE type_name = 'pre_operation'), '15. Drone Flight Safety Certificate', 15, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'pre_operation'), '16. AI Algorithm Compliance Certificate', 16, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'pre_operation'), '17. Crypto Security Audit Certificate', 17, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'pre_operation'), '18. Solar Energy Generation License', 18, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'pre_operation'), '19. Digital Payment Gateway Approval', 19, TRUE);

-- =====================================================
-- 7. CREATE NEW OPTION CATEGORY
-- =====================================================

-- Step 1: Create a new master data type
INSERT INTO master_data_types (type_name, display_name, description) 
VALUES (
    'business_sizes',
    'What is your business size?',
    'Business size categories for different regulations'
);

-- Step 2: Add options for the new category
INSERT INTO master_data (type_id, value, order_num, is_active) VALUES
((SELECT id FROM master_data_types WHERE type_name = 'business_sizes'), 'Micro Enterprise (< 10 employees)', 1, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'business_sizes'), 'Small Enterprise (10-50 employees)', 2, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'business_sizes'), 'Medium Enterprise (50-250 employees)', 3, TRUE),
((SELECT id FROM master_data_types WHERE type_name = 'business_sizes'), 'Large Enterprise (250+ employees)', 4, TRUE);

-- Step 3: Add to step flow (optional - if you want it in the chatbot flow)
INSERT INTO step_flow (step_number, type_id, next_step, response_type, input_caption, dependent_on_step, dependent_on_value)
VALUES (
    6,  -- New step number
    (SELECT id FROM master_data_types WHERE type_name = 'business_sizes'),
    7,  -- Next step
    'options',
    'What is your business size?',
    5,  -- Depends on step 5
    'Building Plan Approval'  -- Only show if user selected this service
);

-- =====================================================
-- 8. ADD CONDITIONAL FLOW STEPS
-- =====================================================

-- Add a conditional step that only appears for specific selections
INSERT INTO step_flow (step_number, type_id, next_step, response_type, input_caption, dependent_on_step, dependent_on_value)
VALUES (
    7,  -- Step number
    (SELECT id FROM master_data_types WHERE type_name = 'sectors'),  -- Reuse sectors for this example
    8,  -- Next step
    'options',
    'Select your specific industry sub-category:',
    1,  -- Depends on step 1 (sector selection)
    'Information & Broadcasting'  -- Only show if user selected this sector
);

-- =====================================================
-- 9. MODIFY EXISTING OPTIONS
-- =====================================================

-- Update an existing option's text
UPDATE master_data 
SET value = 'INR 2.5 Cr. to INR 10 Cr. (Updated Range)'
WHERE type_id = (SELECT id FROM master_data_types WHERE type_name = 'investments')
AND value = 'INR 2.5 Cr. to INR 10 Cr.';

-- Change the order of an option
UPDATE master_data 
SET order_num = 1
WHERE type_id = (SELECT id FROM master_data_types WHERE type_name = 'sectors')
AND value = 'Information & Broadcasting';

-- Disable an option (hide it without deleting)
UPDATE master_data 
SET is_active = FALSE
WHERE type_id = (SELECT id FROM master_data_types WHERE type_name = 'sectors')
AND value = 'Defence';

-- Re-enable a disabled option
UPDATE master_data 
SET is_active = TRUE
WHERE type_id = (SELECT id FROM master_data_types WHERE type_name = 'sectors')
AND value = 'Defence';

-- =====================================================
-- 10. MODIFY STEP FLOW CONFIGURATION
-- =====================================================

-- Update a step's question text
UPDATE step_flow 
SET input_caption = 'Please select your business sector or industry:'
WHERE step_number = 1 AND dependent_on_step IS NULL;

-- Change response type for a step
UPDATE step_flow 
SET response_type = 'text'
WHERE step_number = 6;

-- Add a new conditional flow
INSERT INTO step_flow (step_number, type_id, next_step, response_type, input_caption, dependent_on_step, dependent_on_value)
VALUES (
    8,
    (SELECT id FROM master_data_types WHERE type_name = 'pre_establishment'),
    9,
    'options',
    'Select additional clearances required:',
    4,  -- Depends on step 4
    'Pre-establishment'
);

-- =====================================================
-- 11. VERIFICATION QUERIES
-- =====================================================

-- Check all sectors
SELECT 'Sectors' as category, value, order_num, is_active 
FROM master_data md
JOIN master_data_types mdt ON md.type_id = mdt.id
WHERE mdt.type_name = 'sectors'
ORDER BY order_num;

-- Check all step flow configurations
SELECT step_number, input_caption, response_type, dependent_on_step, dependent_on_value
FROM step_flow sf
JOIN master_data_types mdt ON sf.type_id = mdt.id
ORDER BY step_number, dependent_on_step;

-- Count options by category
SELECT mdt.type_name, mdt.display_name, COUNT(md.id) as option_count
FROM master_data_types mdt
LEFT JOIN master_data md ON mdt.id = md.type_id AND md.is_active = TRUE
GROUP BY mdt.type_name, mdt.display_name
ORDER BY mdt.type_name;

-- =====================================================
-- 12. BULK OPERATIONS
-- =====================================================

-- Disable all options in a category (for maintenance)
UPDATE master_data 
SET is_active = FALSE
WHERE type_id = (SELECT id FROM master_data_types WHERE type_name = 'pre_establishment');

-- Re-enable all options in a category
UPDATE master_data 
SET is_active = TRUE
WHERE type_id = (SELECT id FROM master_data_types WHERE type_name = 'pre_establishment');

-- Delete a complete category (use with caution!)
-- DELETE FROM master_data WHERE type_id = (SELECT id FROM master_data_types WHERE type_name = 'category_to_delete');
-- DELETE FROM master_data_types WHERE type_name = 'category_to_delete';

-- =====================================================
-- IMPORTANT NOTES:
-- =====================================================
-- 1. After adding new options, they appear immediately in the chatbot
-- 2. No code changes or server restart required
-- 3. Use order_num to control the display sequence
-- 4. Use is_active to temporarily hide options
-- 5. Always test new conditional flows thoroughly
-- 6. Backup database before bulk operations
-- =====================================================
